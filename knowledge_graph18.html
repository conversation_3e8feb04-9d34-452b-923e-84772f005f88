<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Codebase Analysis Report</title>
    <style>
        body { font-family: sans-serif; display: flex; flex-direction: column; align-items: center; }
        #report-container { display: flex; width: 98%; gap: 16px; margin-top: 20px; }
        #graph-container { flex-grow: 1; border: 1px solid #ccc; padding: 10px; border-radius: 8px; }
        #sidebar { width: 400px; flex-shrink: 0; border: 1px solid #ccc; padding: 10px; border-radius: 8px; max-height: 85vh; overflow-y: auto; }
        h1, h2 { color: #333; }
        #analysis-report ul { list-style-type: none; padding-left: 0; }
        #analysis-report h3 { border-bottom: 1px solid #eee; padding-bottom: 5px; margin-top: 20px;}
        #analysis-report li { background-color: #f9f9f9; border: 1px solid #eee; padding: 8px; margin-bottom: 5px; border-radius: 4px; }
        #analysis-report strong { color: #c0392b; }
        /* Mermaid Node Styling */
        .fileNode { fill:#FFFACD; stroke:#FFD700; stroke-width:2px; }
        .functionNode { fill:#87CEEB; stroke:#4682B4; stroke-width:2px; }
        .importNode { fill:#90EE90; stroke:#2E8B57; stroke-width:2px; }
    </style>
</head>
<body>
    <h1>Codebase Analysis Report</h1>
    <div id="report-container">
        <div id="graph-container">
            <h2>Knowledge Graph</h2>
            <div class="mermaid">
graph TD
    %% File Nodes
    main_py("main.py<br/>232 lines"):::fileNode
    requirements_txt("requirements.txt<br/>6 dependencies"):::fileNode
    spec_prompt_md("spec-prompt.md<br/>Project spec"):::fileNode
    sample_txt("sample.txt<br/>3874 lines"):::fileNode

    %% Function Nodes
    parse_args("parse_args()<br/>lines 14-25"):::functionNode
    chunk_file("chunk_file()<br/>lines 27-35"):::functionNode
    get_nlp("get_nlp()<br/>lines 37-50"):::functionNode
    get_hf_ner_pipeline("get_hf_ner_pipeline()<br/>lines 52-65"):::functionNode
    process_chunk_spacy("process_chunk_spacy()<br/>lines 67-85"):::functionNode
    process_chunk_hf("process_chunk_hf()<br/>lines 87-105"):::functionNode
    process_chunk("process_chunk()<br/>lines 107-117"):::functionNode
    worker_thread("worker_thread()<br/>lines 140-232"):::functionNode
    test_spacy_ner("test_spacy_ner()<br/>lines 119-130"):::functionNode
    save_output("save_output()<br/>lines 132-138"):::functionNode

    %% Import/Dependency Nodes
    argparse_mod("argparse"):::importNode
    threading_mod("threading"):::importNode
    queue_mod("queue.Queue"):::importNode
    spacy_mod("spacy"):::importNode
    transformers_mod("transformers"):::importNode
    torch_mod("torch"):::importNode
    json_mod("json"):::importNode
    os_mod("os"):::importNode
    datetime_mod("datetime"):::importNode

    %% Subgraphs for organization
    subgraph Core_Files ["📁 Core Files"]
        main_py
        requirements_txt
        spec_prompt_md
        sample_txt
    end

    subgraph Entry_Functions ["🚀 Entry & Setup"]
        parse_args
        chunk_file
        test_spacy_ner
    end

    subgraph NLP_Backends ["🧠 NLP Processing Backends"]
        get_nlp
        get_hf_ner_pipeline
        process_chunk_spacy
        process_chunk_hf
        process_chunk
    end

    subgraph Threading_Layer ["⚡ Threading & Output"]
        worker_thread
        save_output
    end

    subgraph Standard_Imports ["📚 Standard Library"]
        argparse_mod
        threading_mod
        queue_mod
        json_mod
        os_mod
        datetime_mod
    end

    subgraph ML_Imports ["🤖 ML Libraries"]
        spacy_mod
        transformers_mod
        torch_mod
    end

    %% ACTUAL CODE RELATIONSHIPS FROM INVENTORY

    %% File Dependencies (from inventory relationships)
    main_py -.->|"DEPENDS_ON"| requirements_txt
    main_py -.->|"PROCESSES"| sample_txt
    spec_prompt_md -.->|"SPECIFIES"| main_py

    %% Import Relationships (from inventory imports)
    main_py -->|"imports"| argparse_mod
    main_py -->|"imports"| threading_mod
    main_py -->|"imports"| queue_mod
    main_py -->|"imports"| spacy_mod
    main_py -->|"imports"| transformers_mod
    main_py -->|"imports"| torch_mod
    main_py -->|"imports"| json_mod
    main_py -->|"imports"| os_mod
    main_py -->|"imports"| datetime_mod

    %% Function Call Relationships (from inventory analysis)
    parse_args -->|"CALLS"| chunk_file
    process_chunk_spacy -->|"CALLS"| get_nlp
    process_chunk_hf -->|"CALLS"| get_hf_ner_pipeline
    worker_thread -->|"CALLS"| process_chunk
    process_chunk -->|"CALLS"| process_chunk_spacy
    process_chunk -->|"CALLS"| process_chunk_hf

    %% Data Flow (actual processing pipeline)
    sample_txt -->|"input data"| chunk_file
    chunk_file -->|"text chunks"| worker_thread
    worker_thread -->|"processes"| process_chunk
    process_chunk_spacy -->|"entities"| save_output
    process_chunk_hf -->|"entities"| save_output

    %% Backend Dependencies
    get_nlp -->|"uses"| spacy_mod
    get_hf_ner_pipeline -->|"uses"| transformers_mod
    get_hf_ner_pipeline -->|"uses"| torch_mod
    save_output -->|"uses"| json_mod
    save_output -->|"uses"| datetime_mod
            </div>
        </div>
        <div id="sidebar">
            <h2>Analysis & Refactoring</h2>
            <div id="analysis-report">
                <h3>🔍 Code Analysis Summary</h3>
<ul>
    <li><strong>Total Files:</strong> 4 source files analyzed</li>
    <li><strong>Functions:</strong> 10 functions in main.py</li>
    <li><strong>Dependencies:</strong> 6 external packages</li>
    <li><strong>Architecture:</strong> Multi-threaded NLP processor with dual backends</li>
</ul>

<h3>🏗️ Architecture Strengths</h3>
<ul>
    <li><strong>Dual Backend Support:</strong> Both spaCy and Hugging Face transformers</li>
    <li><strong>Apple Silicon Optimization:</strong> GPU acceleration configured</li>
    <li><strong>Thread Safety:</strong> Proper use of thread-local storage</li>
    <li><strong>Configurable Processing:</strong> Chunk size and worker count parameters</li>
    <li><strong>Clean Separation:</strong> Backend-specific processing functions</li>
</ul>

<h3>⚠️ Potential Refactoring Opportunities</h3>
<ul>
    <li><strong>Large Main Function:</strong> Main execution block (lines 140-232) could be extracted into smaller functions</li>
    <li><strong>Duplicate Code:</strong> Entity deduplication logic could be extracted to a utility function</li>
    <li><strong>Error Handling:</strong> Limited error handling throughout the application</li>
    <li><strong>Configuration Management:</strong> Hard-coded values could be moved to configuration</li>
    <li><strong>Logging:</strong> Basic print statements could be replaced with proper logging</li>
</ul>

<h3>🔧 Suggested Improvements</h3>
<ul>
    <li><strong>Extract Classes:</strong> Create NLPProcessor and EntityExtractor classes</li>
    <li><strong>Add Configuration:</strong> Create config.py for settings management</li>
    <li><strong>Improve Error Handling:</strong> Add try-catch blocks and graceful degradation</li>
    <li><strong>Add Unit Tests:</strong> Create test suite for core functions</li>
    <li><strong>Documentation:</strong> Add docstrings and type hints</li>
</ul>

<h3>📊 Dependency Analysis</h3>
<ul>
    <li><strong>Core ML Libraries:</strong> spacy[apple], torch, transformers</li>
    <li><strong>Parsing Libraries:</strong> tree-sitter ecosystem (potentially unused)</li>
    <li><strong>Standard Libraries:</strong> argparse, threading, json, os, datetime</li>
    <li><strong>Optimization:</strong> Apple Silicon specific configurations present</li>
</ul>

<h3>🎯 Performance Considerations</h3>
<ul>
    <li><strong>Memory Management:</strong> Large text file processing with chunking</li>
    <li><strong>Thread Pool:</strong> Configurable worker threads for parallel processing</li>
    <li><strong>GPU Utilization:</strong> Apple Metal backend for spaCy and MPS for transformers</li>
    <li><strong>Output Optimization:</strong> Entity deduplication and concept ranking</li>
</ul>

<h3>🔄 Code Reusability</h3>
<ul>
    <li><strong>Modular Functions:</strong> Well-separated processing functions</li>
    <li><strong>Backend Abstraction:</strong> Clean dispatcher pattern for backend selection</li>
    <li><strong>Thread-Safe Design:</strong> Proper isolation of NLP pipeline instances</li>
    <li><strong>Configurable Parameters:</strong> Command-line interface for key settings</li>
</ul>

<h3>📈 Scalability Notes</h3>
<ul>
    <li><strong>Horizontal Scaling:</strong> Thread-based parallelism implemented</li>
    <li><strong>Memory Efficiency:</strong> Chunk-based processing prevents memory overflow</li>
    <li><strong>Backend Flexibility:</strong> Easy to add new NLP backends</li>
    <li><strong>Output Management:</strong> Timestamped output files for tracking</li>
</ul>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
              curve: 'basis'
            },
            htmlLabels: true
        });
    </script>
</body>
</html>
