<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Codebase Analysis Report</title>
    <style>
        body { font-family: sans-serif; display: flex; flex-direction: column; align-items: center; }
        #report-container { display: flex; width: 98%; gap: 16px; margin-top: 20px; }
        #graph-container { flex-grow: 1; border: 1px solid #ccc; padding: 10px; border-radius: 8px; }
        #sidebar { width: 400px; flex-shrink: 0; border: 1px solid #ccc; padding: 10px; border-radius: 8px; max-height: 85vh; overflow-y: auto; }
        h1, h2 { color: #333; }
        #analysis-report ul { list-style-type: none; padding-left: 0; }
        #analysis-report h3 { border-bottom: 1px solid #eee; padding-bottom: 5px; margin-top: 20px;}
        #analysis-report li { background-color: #f9f9f9; border: 1px solid #eee; padding: 8px; margin-bottom: 5px; border-radius: 4px; }
        #analysis-report strong { color: #c0392b; }
        /* Mermaid Node Styling */
        .classNode { fill:#DDA0DD; stroke:#8A2BE2; stroke-width:2px; }
        .functionNode { fill:#87CEEB; stroke:#4682B4; stroke-width:2px; }
        .dependencyNode { fill:#90EE90; stroke:#2E8B57; stroke-width:2px; }
        .fileNode { fill:#FFFACD; stroke:#FFD700; stroke-width:2px; }
    </style>
</head>
<body>
    <h1>Codebase Analysis Report</h1>
    <div id="report-container">
        <div id="graph-container">
            <h2>Knowledge Graph</h2>
            <div class="mermaid">
graph TD
    %% ALL NODE DEFINITIONS FROM INVENTORY
    main_py("main.py"):::fileNode
    requirements_txt("requirements.txt"):::dependencyNode
    sample_txt("sample.txt"):::fileNode
    spec_prompt_md("spec-prompt.md"):::fileNode
    output_json("output/*.json"):::fileNode

    %% FUNCTION NODES FROM INVENTORY
    parse_args_func("parse_args()"):::functionNode
    chunk_file_func("chunk_file()"):::functionNode
    get_nlp_func("get_nlp()"):::functionNode
    get_hf_ner_pipeline_func("get_hf_ner_pipeline()"):::functionNode
    process_chunk_spacy_func("process_chunk_spacy()"):::functionNode
    process_chunk_hf_func("process_chunk_hf()"):::functionNode
    process_chunk_func("process_chunk()"):::functionNode
    worker_thread_func("worker_thread()"):::functionNode
    test_spacy_ner_func("test_spacy_ner()"):::functionNode
    save_output_func("save_output()"):::functionNode
    main_execution("main execution block"):::functionNode

    %% EXTERNAL DEPENDENCIES FROM INVENTORY
    spacy_lib("spaCy Library"):::dependencyNode
    torch_lib("PyTorch"):::dependencyNode
    transformers_lib("Transformers"):::dependencyNode

    %% SUBGRAPHS
    subgraph source_logic ["Source & Logic"]
        main_py
        parse_args_func
        chunk_file_func
        get_nlp_func
        get_hf_ner_pipeline_func
        process_chunk_spacy_func
        process_chunk_hf_func
        process_chunk_func
        worker_thread_func
        test_spacy_ner_func
        save_output_func
        main_execution
    end

    subgraph dependencies_config ["Dependencies & Config"]
        requirements_txt
        spacy_lib
        torch_lib
        transformers_lib
    end

    subgraph data_documentation ["Data & Documentation"]
        sample_txt
        spec_prompt_md
        output_json
    end

    %% RELATIONSHIPS FROM INVENTORY - IMPORTS
    main_py -.->|"uses_nlp_library"| spacy_lib
    main_py -.->|"uses_ml_framework"| torch_lib
    main_py -.->|"uses_ml_library"| transformers_lib
    requirements_txt -->|"defines_dependencies_for"| main_py

    %% RELATIONSHIPS FROM INVENTORY - DATA FLOW
    sample_txt -->|"input_data_for"| main_py
    main_py -->|"creates_output_files"| output_json
    spec_prompt_md -.->|"defines_requirements_for"| main_py

    %% RELATIONSHIPS FROM INVENTORY - FUNCTION CALLS
    main_execution -->|"CALLS"| parse_args_func
    main_execution -->|"CALLS"| chunk_file_func
    worker_thread_func -->|"CALLS"| process_chunk_func
    process_chunk_func -->|"CALLS"| process_chunk_spacy_func
    process_chunk_func -->|"CALLS"| process_chunk_hf_func
    process_chunk_spacy_func -->|"CALLS"| get_nlp_func
    process_chunk_hf_func -->|"CALLS"| get_hf_ner_pipeline_func

    %% ADDITIONAL LOGICAL RELATIONSHIPS
    main_py --> main_execution
    main_execution --> worker_thread_func
    main_execution --> test_spacy_ner_func
    main_execution --> save_output_func
            </div>
        </div>
        <div id="sidebar">
            <h2>Analysis & Refactoring</h2>
            <div id="analysis-report">
                <h3>🏗️ Architecture Overview</h3>
                <ul>
                    <li><strong>Project Type:</strong> Apple Silicon NLP Processing PoC</li>
                    <li><strong>Primary Language:</strong> Python (232 lines)</li>
                    <li><strong>Threading Model:</strong> Multi-threaded with Queue-based processing</li>
                    <li><strong>Backend Support:</strong> Dual (spaCy + Hugging Face)</li>
                </ul>

                <h3>🔧 Design Patterns Identified</h3>
                <ul>
                    <li><strong>Factory Pattern:</strong> get_nlp() and get_hf_ner_pipeline() for backend abstraction</li>
                    <li><strong>Strategy Pattern:</strong> process_chunk() dispatcher for algorithm selection</li>
                    <li><strong>Producer-Consumer:</strong> Queue-based threading for parallel processing</li>
                    <li><strong>Thread-Local Storage:</strong> Thread-safe NLP pipeline management</li>
                </ul>

                <h3>🚀 Apple Silicon Optimizations</h3>
                <ul>
                    <li><strong>spacy[apple]:</strong> Native Apple Silicon spaCy support</li>
                    <li><strong>torch MPS backend:</strong> Apple GPU acceleration</li>
                    <li><strong>transformers device=0:</strong> GPU device selection for BERT</li>
                </ul>

                <h3>📊 Code Quality Analysis</h3>
                <ul>
                    <li><strong>Thread Safety:</strong> ✅ Implemented via thread-local storage</li>
                    <li><strong>Error Handling:</strong> ✅ Model download fallback, GPU detection</li>
                    <li><strong>Configurability:</strong> ✅ CLI args for chunk size, workers, backend</li>
                    <li><strong>Modularity:</strong> ✅ Well-separated functions for different concerns</li>
                </ul>

                <h3>🔍 Potential Refactoring Opportunities</h3>
                <ul>
                    <li><strong>Class-based Architecture:</strong> Consider creating NLPProcessor class to encapsulate backend logic</li>
                    <li><strong>Configuration Management:</strong> Extract hardcoded values (model names, allowed labels) to config</li>
                    <li><strong>Result Processing:</strong> Separate deduplication and aggregation logic into dedicated functions</li>
                    <li><strong>Testing:</strong> Add unit tests for individual processing functions</li>
                </ul>

                <h3>📈 Performance Considerations</h3>
                <ul>
                    <li><strong>Memory Usage:</strong> Large text file (3874 lines) processed in configurable chunks</li>
                    <li><strong>Parallel Processing:</strong> Configurable worker threads for spaCy backend</li>
                    <li><strong>Sequential Processing:</strong> HF backend uses single pipeline to avoid memory issues</li>
                    <li><strong>GPU Utilization:</strong> Both backends configured for Apple Silicon GPU acceleration</li>
                </ul>

                <h3>🔗 Dependency Analysis</h3>
                <ul>
                    <li><strong>Core ML Libraries:</strong> spaCy, PyTorch, Transformers (all Apple Silicon optimized)</li>
                    <li><strong>Standard Library Usage:</strong> Extensive use of threading, argparse, json, os</li>
                    <li><strong>Tree-sitter Dependencies:</strong> Present but unused in current implementation</li>
                </ul>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing Mermaid...');
            try {
                mermaid.initialize({
                    startOnLoad: true,
                    theme: 'default',
                    flowchart: {
                        curve: 'basis'
                    },
                    htmlLabels: true
                });
                console.log('Mermaid initialized successfully');
            } catch (error) {
                console.error('Mermaid initialization error:', error);
            }
        });
    </script>
</body>
</html>
