<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Simple Mermaid Test</title>
    <style>
        .fileNode { fill:#FFFACD; stroke:#FFD700; stroke-width:2px; }
        .functionNode { fill:#87CEEB; stroke:#4682B4; stroke-width:2px; }
        .dependencyNode { fill:#90EE90; stroke:#2E8B57; stroke-width:2px; }
    </style>
</head>
<body>
    <h1>Simple Test</h1>
    
    <div class="mermaid">
graph TD
    main_py("main.py"):::fileNode
    parse_args("parse_args()"):::functionNode
    requirements("requirements.txt"):::dependencyNode
    
    main_py --> parse_args
    requirements --> main_py
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing Mermaid...');
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default'
            });
        });
    </script>
</body>
</html>
